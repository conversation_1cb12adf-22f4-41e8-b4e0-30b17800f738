import type { FieldValues } from "react-hook-form";
import { Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { useFormContext } from "./formContext";
import { FormField } from "./formField";
import type { BaseFormFieldProps } from "./types";

export type InputFieldProps<T extends FieldValues> = BaseFormFieldProps<T> & {
    startAdornment?: React.ReactNode;
    endAdornment?: React.ReactNode;
} & React.InputHTMLAttributes<HTMLInputElement>;

export function Text<T extends FieldValues>({
    name,
    label,
    startAdornment,
    endAdornment,
    readonly,
    ...rest
}: InputFieldProps<T>) {
    const { editMode, control, errors } = useFormContext<T>();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormField label={label} error={errors[name]?.message}>
                    <div className="relative">
                        {startAdornment && (
                            <div className="text-muted-foreground absolute inset-y-0 left-0 flex items-center pl-3">
                                {startAdornment}
                            </div>
                        )}
                        <Input
                            {...rest}
                            {...field}
                            readOnly={readonly}
                            className={[startAdornment ? "pl-9" : "", endAdornment ? "pr-9" : ""]
                                .filter(Boolean)
                                .join(" ")}
                        />
                        {endAdornment && (
                            <div className="text-muted-foreground absolute inset-y-0 right-0 flex items-center pr-3">
                                {endAdornment}
                            </div>
                        )}
                    </div>
                </FormField>
            )}
        />
    );
}
